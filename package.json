{"name": "learn-elysia", "version": "0.0.0", "scripts": {"db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:rollback": "drizzle-kit drop", "db:studio": "drizzle-kit studio --port 4000", "dev": "bun run --watch src/index.ts", "format": "biome format --write", "test": "bun test", "test:watch": "bun test --watch", "test:coverage": "bun test --coverage"}, "dependencies": {"@elysiajs/cookie": "^0.8.0", "@elysiajs/html": "^1.3.1", "@elysiajs/jwt": "^1.3.2", "drizzle-orm": "^0.44.4", "drizzle-zod": "^0.8.3", "elysia": "latest", "oauth-1.0a": "^2.2.6", "zod": "^4.0.17"}, "devDependencies": {"@biomejs/biome": "2.1.4", "@kitajs/ts-html-plugin": "^4.1.2", "better-sqlite3": "12.2.0", "bun-types": "1.2.20", "drizzle-kit": "^0.31.4"}}