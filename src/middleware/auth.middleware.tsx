import { Elysia } from 'elysia'
import { Html } from '@elysiajs/html'
import { cookie } from '@elysiajs/cookie'
import { AuthService } from '~/services/auth.service'
import { Layout } from '~/views/layout'
import type { User } from '~/models/schema'

// Create a properly typed auth plugin
export const authMiddleware = new Elysia({ name: 'auth' })
  .use(cookie())
  .derive(async ({ cookie: { session } }) => {
    let user: User | null = null

    if (session?.value) {
      try {
        user = await AuthService.getUserBySession(session.value)
        if (!user) {
          // Invalid or expired session, remove cookie
          session.remove()
        }
      } catch (error) {
        console.error('Session validation error:', error)
        session.remove()
      }
    }

    console.log('authMiddleware - user:', user)
    return {
      user,
    }
  })

// The key issue: we need to properly access the derived context
// In Elysia, derived context is available in the handler parameters
export const requireAuth = (app: any) =>
  app
    .use(authMiddleware)
    .onBeforeHandle(({ user, set }: any) => {
      console.log('requireAuth middleware - user:', user)
      if (!user) {
        set.status = 200
        set.headers['content-type'] = 'text/html; charset=utf8'
        return (
          <Layout>
            <div class="max-w-md w-full space-y-8">
              <div class="text-center">
                <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                  Authentication Required
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                  You need to be logged in to access this page.
                </p>
              </div>
              <div class="bg-white py-8 px-6 shadow rounded-lg">
                <div class="space-y-6">
                  <a
                    href="/auth/twitter"
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                  >
                    Login with Twitter
                  </a>
                  <a
                    href="/"
                    class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                  >
                    Back to Home
                  </a>
                </div>
              </div>
            </div>
          </Layout>
        )
      }
    })

// Alternative approach: Create a proper Elysia plugin
export const requireAuthPlugin = new Elysia({ name: 'requireAuth' })
  .use(authMiddleware)
  .macro(({ onBeforeHandle }) => ({
    requireAuth() {
      onBeforeHandle(({ user, set }: any) => {
        console.log('requireAuthPlugin - user:', user)
        if (!user) {
          set.status = 200
          set.headers['content-type'] = 'text/html; charset=utf8'
          return (
            <Layout>
              <div class="max-w-md w-full space-y-8">
                <div class="text-center">
                  <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                    Authentication Required
                  </h2>
                  <p class="mt-2 text-sm text-gray-600">
                    You need to be logged in to access this page.
                  </p>
                </div>
                <div class="bg-white py-8 px-6 shadow rounded-lg">
                  <div class="space-y-6">
                    <a
                      href="/auth/twitter"
                      class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                    >
                      Login with Twitter
                    </a>
                    <a
                      href="/"
                      class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                    >
                      Back to Home
                    </a>
                  </div>
                </div>
              </div>
            </Layout>
          )
        }
      })
    }
  }))
