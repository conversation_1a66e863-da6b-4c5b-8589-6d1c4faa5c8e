import { Html } from '@elysiajs/html'
import { Elysia } from 'elysia'
import { Layout } from '~/views/layout'

export const homeController = new Elysia()
  .get('/', async () => {
    return (
      <Layout>
        <div class="max-w-md w-full space-y-8">
          <div class="text-center">
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
              Twitter Auth App
            </h2>
          </div>

          <div class="bg-white py-8 px-6 shadow rounded-lg">
            <div class="space-y-6">
              <a
                href="#"
                class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
              >
                Get Started
              </a>
            </div>
          </div>
        </div>
      </Layout>
    )
  })

  .get(
    '/profile',
    () => (
      <Layout>
        <div class="max-w-md w-full space-y-8">
          <div class="text-center">
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
              Profile
            </h2>
          </div>

          <div class="bg-white py-8 px-6 shadow rounded-lg">
            <div class="space-y-6">
              The Profile Page
            </div>
          </div>
        </div>
      </Layout>
    )
  )
