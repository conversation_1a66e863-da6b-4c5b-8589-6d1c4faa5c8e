import { Html } from '@elysiajs/html'
import { Elysia } from 'elysia'
import { Layout } from '~/views/layout'
import { authMiddleware } from '~/middleware/auth.middleware'

export const homeController = new Elysia()
  .use(authMiddleware)
  .get('/', async (context: any) => {
    // Now we can access the derived user context
    const { user } = context
    console.log('Home route - derived user:', user)
    return (
      <Layout>
        <div class="max-w-md w-full space-y-8">
          <div class="text-center">
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
              Twitter Auth App
            </h2>
            {user && (
              <p class="mt-2 text-sm text-gray-600">
                Welcome back, {user.displayName}!
              </p>
            )}
          </div>

          <div class="bg-white py-8 px-6 shadow rounded-lg">
            <div class="space-y-6">
              {user ? (
                <div class="space-y-4">
                  <a
                    href="/profile"
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-200"
                  >
                    View Profile
                  </a>
                  <a
                    href="/auth/logout"
                    class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                  >
                    Logout
                  </a>
                </div>
              ) : (
                <a
                  href="/auth/twitter"
                  class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                >
                  Login with Twitter
                </a>
              )}
            </div>
          </div>
        </div>
      </Layout>
    )
  })

  .get('/profile', async (context: any) => {
    // Test derived context
    const { user, set } = context
    console.info('Profile route - derived user:', user)

    // If no user, show authentication required
    if (!user) {
      set.status = 200
      set.headers['content-type'] = 'text/html; charset=utf8'
      return (
        <Layout>
          <div class="max-w-md w-full space-y-8">
            <div class="text-center">
              <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Authentication Required
              </h2>
              <p class="mt-2 text-sm text-gray-600">
                You need to be logged in to access this page.
              </p>
            </div>
            <div class="bg-white py-8 px-6 shadow rounded-lg">
              <div class="space-y-6">
                <a
                  href="/auth/twitter"
                  class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                >
                  Login with Twitter
                </a>
                <a
                  href="/"
                  class="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                >
                  Back to Home
                </a>
              </div>
            </div>
          </div>
        </Layout>
      )
    }

    const authPayload = user?.authPayload as any
    return (
      <Layout>
        <div class="max-w-4xl w-full space-y-8">
          <div class="text-center">
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">Profile</h2>
            <p class="mt-2 text-sm text-gray-600">
              Welcome, {user?.displayName}!
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* User Information */}
            <div class="bg-white py-8 px-6 shadow rounded-lg">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                User Information
              </h3>
              <div class="space-y-4">
                <div class="flex items-center space-x-4">
                  {user?.profileImageUrl && (
                    <img
                      src={user.profileImageUrl}
                      alt="Profile"
                      class="w-16 h-16 rounded-full"
                    />
                  )}
                  <div>
                    <p class="text-lg font-semibold text-gray-900">
                      {user?.displayName}
                    </p>
                    <p class="text-sm text-gray-600">@{user?.username}</p>
                    {user?.email && (
                      <p class="text-sm text-gray-600">{user.email}</p>
                    )}
                  </div>
                </div>
                <div class="text-sm text-gray-500">
                  <p>Twitter ID: {user?.twitterId}</p>
                  <p>
                    Member since:{' '}
                    {user?.createdAt
                      ? new Date(user.createdAt).toLocaleDateString()
                      : 'Unknown'}
                  </p>
                  <p>
                    Last updated:{' '}
                    {user?.updatedAt
                      ? new Date(user.updatedAt).toLocaleDateString()
                      : 'Unknown'}
                  </p>
                </div>
              </div>
            </div>

            {/* Authentication Payload */}
            <div class="bg-white py-8 px-6 shadow rounded-lg">
              <h3 class="text-lg font-medium text-gray-900 mb-4">
                Authentication Data
              </h3>
              <div class="space-y-4">
                {authPayload && (
                  <div>
                    <p class="text-sm font-medium text-gray-700 mb-2">
                      Authenticated at:{' '}
                      {new Date(authPayload.authenticatedAt).toLocaleString()}
                    </p>
                    <div class="bg-gray-50 p-4 rounded-md">
                      <pre class="text-xs text-gray-800 whitespace-pre-wrap overflow-auto max-h-96">
                        {JSON.stringify(authPayload, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div class="bg-white py-8 px-6 shadow rounded-lg">
            <div class="flex space-x-4 justify-center">
              <a
                href="/"
                class="flex justify-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
              >
                Back to Home
              </a>
              <a
                href="/auth/logout"
                class="flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-200"
              >
                Logout
              </a>
            </div>
          </div>
        </div>
      </Layout>
    )
  })
