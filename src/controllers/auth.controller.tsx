import { Elysia, t } from 'elysia'
import { cookie } from '@elysiajs/cookie'
import { AuthService } from '~/services/auth.service'
import { Layout } from '~/views/layout'

// In-memory store for OAuth tokens (in production, use Redis or database)
const oauthTokenStore = new Map<string, { tokenSecret: string; timestamp: number }>()

// Clean up expired tokens every 10 minutes
setInterval(() => {
  const now = Date.now()
  for (const [token, data] of oauthTokenStore.entries()) {
    // Remove tokens older than 10 minutes
    if (now - data.timestamp > 10 * 60 * 1000) {
      oauthTokenStore.delete(token)
    }
  }
}, 10 * 60 * 1000)

export const authController = new Elysia({ prefix: '/auth' })
  .use(cookie())
  
  // Initiate Twitter OAuth flow
  .get('/twitter', async ({ redirect }) => {
    try {
      const { token, tokenSecret, authUrl } = await AuthService.getRequestToken()
      
      // Store token secret temporarily
      oauthTokenStore.set(token, {
        tokenSecret,
        timestamp: Date.now(),
      })
      
      return redirect(authUrl)
    } catch (error) {
      console.error('Twitter OAuth initiation error:', error)
      return (
        <Layout>
          <div class="max-w-md w-full space-y-8">
            <div class="text-center">
              <h2 class="mt-6 text-3xl font-extrabold text-red-600">
                Authentication Error
              </h2>
              <p class="mt-2 text-sm text-gray-600">
                Failed to initiate Twitter authentication. Please try again.
              </p>
            </div>
            <div class="bg-white py-8 px-6 shadow rounded-lg">
              <div class="space-y-6">
                <a
                  href="/"
                  class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                >
                  Back to Home
                </a>
              </div>
            </div>
          </div>
        </Layout>
      )
    }
  })
  
  // Handle Twitter OAuth callback
  .get(
    '/twitter/callback',
    async ({ query, cookie: { session }, redirect, set }) => {
      try {
        const { oauth_token, oauth_verifier } = query
        
        if (!oauth_token || !oauth_verifier) {
          throw new Error('Missing OAuth parameters')
        }
        
        // Get stored token secret
        const storedData = oauthTokenStore.get(oauth_token)
        if (!storedData) {
          throw new Error('Invalid or expired OAuth token')
        }
        
        // Clean up stored token
        oauthTokenStore.delete(oauth_token)
        
        // Exchange for access token
        const tokens = await AuthService.getAccessToken(
          oauth_token,
          oauth_verifier,
          storedData.tokenSecret
        )
        
        // Get user information
        const twitterUser = await AuthService.getTwitterUser(tokens)
        
        // Create auth payload to store
        const authPayload = {
          tokens,
          twitterUser,
          authenticatedAt: new Date().toISOString(),
        }
        
        // Create or update user
        const user = await AuthService.createOrUpdateUser(twitterUser, authPayload)
        
        // Create session
        const sessionId = await AuthService.createSession(user.id)
        
        // Set session cookie
        session.set({
          value: sessionId,
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 30 * 24 * 60 * 60, // 30 days
        })
        
        return redirect('/profile')
      } catch (error) {
        console.error('Twitter OAuth callback error:', error)
        return (
          <Layout>
            <div class="max-w-md w-full space-y-8">
              <div class="text-center">
                <h2 class="mt-6 text-3xl font-extrabold text-red-600">
                  Authentication Failed
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                  There was an error processing your Twitter authentication. Please try again.
                </p>
              </div>
              <div class="bg-white py-8 px-6 shadow rounded-lg">
                <div class="space-y-6">
                  <a
                    href="/"
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                  >
                    Back to Home
                  </a>
                </div>
              </div>
            </div>
          </Layout>
        )
      }
    },
    {
      query: t.Object({
        oauth_token: t.String(),
        oauth_verifier: t.String(),
      }),
    }
  )
  
  // Logout
  .post('/logout', async ({ cookie: { session }, redirect }) => {
    const sessionId = session.value
    
    if (sessionId) {
      await AuthService.deleteSession(sessionId)
      session.remove()
    }
    
    return redirect('/')
  })
  
  // Logout via GET for convenience
  .get('/logout', async ({ cookie: { session }, redirect }) => {
    const sessionId = session.value
    
    if (sessionId) {
      await AuthService.deleteSession(sessionId)
      session.remove()
    }
    
    return redirect('/')
  })
