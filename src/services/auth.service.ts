import OAuth from 'oauth-1.0a'
import crypto from 'crypto'
import { eq } from 'drizzle-orm'
import { db } from '~/models'
import { users, sessions, type User, type NewUser } from '~/models/schema'
import { ENV } from '~/config'

// Twitter OAuth 1.0a configuration
const oauth = new OAuth({
  consumer: {
    key: ENV.TWITTER_CONSUMER_KEY,
    secret: ENV.TWITTER_CONSUMER_SECRET,
  },
  signature_method: 'HMAC-SHA1',
  hash_function(base_string, key) {
    return crypto.createHmac('sha1', key).update(base_string).digest('base64')
  },
})

// Twitter API endpoints
const TWITTER_REQUEST_TOKEN_URL = 'https://api.twitter.com/oauth/request_token'
const TWITTER_AUTHORIZE_URL = 'https://api.twitter.com/oauth/authorize'
const TWITTER_ACCESS_TOKEN_URL = 'https://api.twitter.com/oauth/access_token'
const TWITTER_VERIFY_CREDENTIALS_URL =
  'https://api.twitter.com/1.1/account/verify_credentials.json'

export interface TwitterUser {
  id_str: string
  screen_name: string
  name: string
  profile_image_url_https: string
  email?: string
}

export interface OAuthTokens {
  oauth_token: string
  oauth_token_secret: string
}

export class AuthService {
  /**
   * Step 1: Get request token from Twitter
   */
  static async getRequestToken(): Promise<{
    token: string
    tokenSecret: string
    authUrl: string
  }> {
    const requestData = {
      url: TWITTER_REQUEST_TOKEN_URL,
      method: 'POST',
      data: {
        oauth_callback: `${ENV.APP_URL}/auth/twitter/callback`,
      },
    }

    const authHeader = oauth.toHeader(oauth.authorize(requestData))

    const response = await fetch(TWITTER_REQUEST_TOKEN_URL, {
      method: 'POST',
      headers: {
        Authorization: authHeader.Authorization,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams(requestData.data).toString(),
    })

    if (!response.ok) {
      throw new Error(`Failed to get request token: ${response.statusText}`)
    }

    const responseText = await response.text()
    const params = new URLSearchParams(responseText)

    const token = params.get('oauth_token')
    const tokenSecret = params.get('oauth_token_secret')

    if (!token || !tokenSecret) {
      throw new Error('Invalid response from Twitter')
    }

    const authUrl = `${TWITTER_AUTHORIZE_URL}?oauth_token=${token}`

    return {
      token,
      tokenSecret,
      authUrl,
    }
  }

  /**
   * Step 2: Exchange request token for access token
   */
  static async getAccessToken(
    oauthToken: string,
    oauthVerifier: string,
    tokenSecret: string,
  ): Promise<OAuthTokens> {
    const requestData = {
      url: TWITTER_ACCESS_TOKEN_URL,
      method: 'POST',
      data: {
        oauth_verifier: oauthVerifier,
      },
    }

    const token = {
      key: oauthToken,
      secret: tokenSecret,
    }

    const authHeader = oauth.toHeader(oauth.authorize(requestData, token))

    const response = await fetch(TWITTER_ACCESS_TOKEN_URL, {
      method: 'POST',
      headers: {
        Authorization: authHeader.Authorization,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams(requestData.data).toString(),
    })

    if (!response.ok) {
      throw new Error(`Failed to get access token: ${response.statusText}`)
    }

    const responseText = await response.text()
    const params = new URLSearchParams(responseText)

    const accessToken = params.get('oauth_token')
    const accessTokenSecret = params.get('oauth_token_secret')

    if (!accessToken || !accessTokenSecret) {
      throw new Error('Invalid access token response from Twitter')
    }

    return {
      oauth_token: accessToken,
      oauth_token_secret: accessTokenSecret,
    }
  }

  /**
   * Step 3: Get user information from Twitter
   */
  static async getTwitterUser(tokens: OAuthTokens): Promise<TwitterUser> {
    const requestData = {
      url: TWITTER_VERIFY_CREDENTIALS_URL,
      method: 'GET',
      data: {
        include_email: 'true',
      },
    }

    const token = {
      key: tokens.oauth_token,
      secret: tokens.oauth_token_secret,
    }

    const authHeader = oauth.toHeader(oauth.authorize(requestData, token))

    const response = await fetch(
      `${TWITTER_VERIFY_CREDENTIALS_URL}?include_email=true`,
      {
        method: 'GET',
        headers: {
          Authorization: authHeader.Authorization,
        },
      },
    )

    if (!response.ok) {
      throw new Error(`Failed to get user info: ${response.statusText}`)
    }

    return await response.json()
  }

  /**
   * Create or update user in database
   */
  static async createOrUpdateUser(
    twitterUser: TwitterUser,
    authPayload: any,
  ): Promise<User> {
    const [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.twitterId, twitterUser.id_str))
      .limit(1)

    if (existingUser) {
      // Update existing user
      const [updatedUser] = await db
        .update(users)
        .set({
          username: twitterUser.screen_name,
          displayName: twitterUser.name,
          profileImageUrl: twitterUser.profile_image_url_https,
          email: twitterUser.email,
          authPayload: authPayload,
          updatedAt: new Date(),
        })
        .where(eq(users.id, existingUser.id))
        .returning()

      return updatedUser as User
    }

    // Create new user
    const newUser: NewUser = {
      twitterId: twitterUser.id_str,
      username: twitterUser.screen_name,
      displayName: twitterUser.name,
      profileImageUrl: twitterUser.profile_image_url_https,
      email: twitterUser.email,
      authPayload: authPayload,
    }

    const [createdUser] = await db.insert(users).values(newUser).returning()

    return createdUser as User
  }

  /**
   * Create session for user
   */
  static async createSession(userId: number): Promise<string> {
    const sessionId = crypto.randomUUID()
    const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days

    await db.insert(sessions).values({
      id: sessionId,
      userId,
      expiresAt,
    })

    return sessionId
  }

  /**
   * Get user by session ID
   */
  static async getUserBySession(sessionId: string): Promise<User | null> {
    const [result] = await db
      .select({
        user: users,
      })
      .from(sessions)
      .innerJoin(users, eq(sessions.userId, users.id))
      .where(eq(sessions.id, sessionId))
      .limit(1)

    if (!result) {
      return null
    }

    // Check if session is expired
    const [session] = await db
      .select()
      .from(sessions)
      .where(eq(sessions.id, sessionId))
      .limit(1)

    if (!session || session.expiresAt < new Date()) {
      // Delete expired session
      await db.delete(sessions).where(eq(sessions.id, sessionId))

      return null
    }

    return result.user as User
  }

  /**
   * Delete session (logout)
   */
  static async deleteSession(sessionId: string): Promise<void> {
    await db.delete(sessions).where(eq(sessions.id, sessionId))
  }
}
