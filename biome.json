{"$schema": "https://biomejs.dev/schemas/2.1.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "useEditorconfig": true}, "linter": {"enabled": true, "rules": {"recommended": true}}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "asNeeded"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}