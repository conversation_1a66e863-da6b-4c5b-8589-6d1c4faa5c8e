/**
 * Test setup file for Bun test environment
 * This file is loaded before all tests to configure the test environment
 */

import { beforeAll, afterAll } from 'bun:test'

// Set test environment variables
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = ':memory:'
process.env.APP_URL = 'http://localhost:3000'
process.env.APP_SECRET = 'test-secret-key-for-testing-only'

// Mock Twitter auth environment variables for tests
process.env.TWITTER_CONSUMER_KEY = 'test-consumer-key'
process.env.TWITTER_CONSUMER_SECRET = 'test-consumer-secret'
process.env.TWITTER_ACCESS_TOKEN = 'test-access-token'
process.env.TWITTER_ACCESS_SECRET = 'test-access-secret'
process.env.TWITTER_CLIENT_ID = 'test-client-id'
process.env.TWITTER_CLIENT_SECRET = 'test-client-secret'

beforeAll(() => {
  console.log('🧪 Setting up test environment...')
})

afterAll(() => {
  console.log('🧹 Cleaning up test environment...')
})
