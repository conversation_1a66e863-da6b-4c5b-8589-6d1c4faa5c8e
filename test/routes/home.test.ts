/**
 * Tests for home controller routes
 */

import { describe, it, expect, beforeAll } from 'bun:test'
import { Elysia } from 'elysia'
import { html } from '@elysiajs/html'
import { cookie } from '@elysiajs/cookie'
import { homeController } from '~/controllers/home.controller'
import {
  expectSuccessfulResponse,
  expectStatus,
  expectHTMLContentType,
  expectHTMLToContain,
  expectValidHTMLStructure,
  expectRequiredMetaTags,
  expectHTMLToContainClass,
  expectTailwindCSS,
  expectTitle,
  expectReasonableResponseTime,
  extractTextFromHTML,
  containsElement,
  isValidHTML
} from '../utils'

describe('Home Controller Routes', () => {
  let app: Elysia

  beforeAll(() => {
    // Create test app with the same plugins as main app
    app = new Elysia()
      .use(html())
      .use(cookie())
      .use(homeController)
  })

  describe('GET /', () => {
    it('should return 200 status code', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      expectStatus(response, 200)
    })

    it('should return HTML content', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      expectHTMLContentType(response)
    })

    it('should return valid HTML structure', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      const html = await response.text()

      expectValidHTMLStructure(html)
      expect(isValidHTML(html)).toBe(true)
    })

    it('should contain required meta tags', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      const html = await response.text()

      expectRequiredMetaTags(html)
    })

    it('should have correct page title', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      const html = await response.text()

      expectTitle(html, 'Login - Twitter Auth App')
    })

    it('should include Tailwind CSS', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      const html = await response.text()

      expectTailwindCSS(html)
    })

    it('should contain main heading', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      const html = await response.text()

      expectHTMLToContain(html, 'Twitter Auth App')
      expect(containsElement(html, 'h2')).toBe(true)
    })

    it('should contain Login with Twitter button', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      const html = await response.text()

      expectHTMLToContain(html, 'Login with Twitter')
      expect(containsElement(html, 'a')).toBe(true)
    })

    it('should have proper CSS classes for styling', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      const html = await response.text()

      expectHTMLToContainClass(html, 'max-w-md')
      expectHTMLToContainClass(html, 'bg-white')
      expectHTMLToContainClass(html, 'text-center')
      expectHTMLToContainClass(html, 'bg-blue-600')
    })

    it('should contain proper layout structure', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      const html = await response.text()

      expectHTMLToContainClass(html, 'bg-gray-100')
      expectHTMLToContainClass(html, 'min-h-screen')
      expectHTMLToContainClass(html, 'flex')
      expectHTMLToContainClass(html, 'items-center')
      expectHTMLToContainClass(html, 'justify-center')
    })

    it('should respond within reasonable time', async () => {
      const startTime = Date.now()
      const response = await app.handle(new Request('http://localhost/'))

      expectSuccessfulResponse(response)
      expectReasonableResponseTime(startTime, 500)
    })

    it('should contain accessible button attributes', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      const html = await response.text()

      // Check for proper button styling and accessibility
      expectHTMLToContain(html, 'focus:outline-none')
      expectHTMLToContain(html, 'focus:ring-2')
      expectHTMLToContain(html, 'transition duration-200')
    })
  })

  describe('GET /profile', () => {
    it('should return 200 status code', async () => {
      const response = await app.handle(new Request('http://localhost/profile'))
      expectStatus(response, 200)
    })

    it('should return HTML content', async () => {
      const response = await app.handle(new Request('http://localhost/profile'))
      expectHTMLContentType(response)
    })

    it('should return valid HTML structure', async () => {
      const response = await app.handle(new Request('http://localhost/profile'))
      const html = await response.text()

      expectValidHTMLStructure(html)
      expect(isValidHTML(html)).toBe(true)
    })

    it('should contain required meta tags', async () => {
      const response = await app.handle(new Request('http://localhost/profile'))
      const html = await response.text()

      expectRequiredMetaTags(html)
    })

    it('should have correct page title', async () => {
      const response = await app.handle(new Request('http://localhost/profile'))
      const html = await response.text()

      expectTitle(html, 'Login - Twitter Auth App')
    })

    it('should include Tailwind CSS', async () => {
      const response = await app.handle(new Request('http://localhost/profile'))
      const html = await response.text()

      expectTailwindCSS(html)
    })

    it('should show authentication required when not logged in', async () => {
      const response = await app.handle(new Request('http://localhost/profile'))
      const html = await response.text()

      expectHTMLToContain(html, 'Authentication Required')
      expect(containsElement(html, 'h2')).toBe(true)
    })

    it('should contain login button when not authenticated', async () => {
      const response = await app.handle(new Request('http://localhost/profile'))
      const html = await response.text()

      expectHTMLToContain(html, 'Login with Twitter')
    })

    it('should have proper CSS classes for styling', async () => {
      const response = await app.handle(new Request('http://localhost/profile'))
      const html = await response.text()

      expectHTMLToContainClass(html, 'max-w-md')
      expectHTMLToContainClass(html, 'bg-white')
      expectHTMLToContainClass(html, 'text-center')
      expectHTMLToContainClass(html, 'space-y-6')
    })

    it('should contain proper layout structure', async () => {
      const response = await app.handle(new Request('http://localhost/profile'))
      const html = await response.text()

      expectHTMLToContainClass(html, 'bg-gray-100')
      expectHTMLToContainClass(html, 'min-h-screen')
      expectHTMLToContainClass(html, 'flex')
      expectHTMLToContainClass(html, 'items-center')
      expectHTMLToContainClass(html, 'justify-center')
    })

    it('should respond within reasonable time', async () => {
      const startTime = Date.now()
      const response = await app.handle(new Request('http://localhost/profile'))

      expectSuccessfulResponse(response)
      expectReasonableResponseTime(startTime, 500)
    })

    it('should have consistent layout with home page', async () => {
      const homeResponse = await app.handle(new Request('http://localhost/'))
      const profileResponse = await app.handle(new Request('http://localhost/profile'))

      const homeHtml = await homeResponse.text()
      const profileHtml = await profileResponse.text()

      // Both should use the same layout structure
      expectHTMLToContainClass(homeHtml, 'bg-gray-100')
      expectHTMLToContainClass(profileHtml, 'bg-gray-100')

      expectHTMLToContainClass(homeHtml, 'max-w-md')
      expectHTMLToContainClass(profileHtml, 'max-w-md')

      // Both should have the same title
      expectTitle(homeHtml, 'Login - Twitter Auth App')
      expectTitle(profileHtml, 'Login - Twitter Auth App')
    })
  })

  describe('Route Error Handling', () => {
    it('should handle non-existent routes gracefully', async () => {
      const response = await app.handle(new Request('http://localhost/non-existent'))

      // Elysia returns 404 for non-existent routes
      expectStatus(response, 404)
    })

    it('should handle different HTTP methods on existing routes', async () => {
      const postResponse = await app.handle(new Request('http://localhost/', { method: 'POST' }))
      const putResponse = await app.handle(new Request('http://localhost/', { method: 'PUT' }))
      const deleteResponse = await app.handle(new Request('http://localhost/', { method: 'DELETE' }))

      // These should return 405 Method Not Allowed or 404
      expect([404, 405]).toContain(postResponse.status)
      expect([404, 405]).toContain(putResponse.status)
      expect([404, 405]).toContain(deleteResponse.status)
    })
  })
})
