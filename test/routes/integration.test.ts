/**
 * Integration tests for the complete Elysia application
 */

import { describe, it, expect, beforeAll } from 'bun:test'
import { Elysia } from 'elysia'
import { html } from '@elysiajs/html'
import { homeController } from '~/controllers/home.controller'
import {
  expectSuccessfulResponse,
  expectStatus,
  expectHTMLContentType,
  expectReasonableResponseTime
} from '../utils'

describe('Application Integration Tests', () => {
  let app: Elysia

  beforeAll(() => {
    // Create the complete app as it would be in production
    app = new Elysia()
      .use(html())
      .use(homeController)
  })

  describe('Application Setup', () => {
    it('should create app instance successfully', () => {
      expect(app).toBeDefined()
      expect(app).toBeInstanceOf(Elysia)
    })

    it('should have html plugin loaded', async () => {
      const response = await app.handle(new Request('http://localhost/'))
      expectHTMLContentType(response)
    })

    it('should have home controller routes registered', async () => {
      const homeResponse = await app.handle(new Request('http://localhost/'))
      const profileResponse = await app.handle(new Request('http://localhost/profile'))
      
      expectSuccessfulResponse(homeResponse)
      expectSuccessfulResponse(profileResponse)
    })
  })

  describe('Route Navigation Flow', () => {
    it('should allow navigation between routes', async () => {
      // Test home route
      const homeResponse = await app.handle(new Request('http://localhost/'))
      expectStatus(homeResponse, 200)
      
      // Test profile route
      const profileResponse = await app.handle(new Request('http://localhost/profile'))
      expectStatus(profileResponse, 200)
      
      // Both should be accessible
      expectSuccessfulResponse(homeResponse)
      expectSuccessfulResponse(profileResponse)
    })

    it('should maintain consistent performance across routes', async () => {
      const routes = ['/', '/profile']
      
      for (const route of routes) {
        const startTime = Date.now()
        const response = await app.handle(new Request(`http://localhost${route}`))
        
        expectSuccessfulResponse(response)
        expectReasonableResponseTime(startTime, 1000)
      }
    })
  })

  describe('Content Consistency', () => {
    it('should use consistent layout across all routes', async () => {
      const routes = ['/', '/profile']
      const responses = await Promise.all(
        routes.map(route => app.handle(new Request(`http://localhost${route}`)))
      )
      
      const htmlContents = await Promise.all(
        responses.map(response => response.text())
      )
      
      // All pages should have the same basic structure
      htmlContents.forEach(html => {
        expect(html).toContain('<html lang="en">')
        expect(html).toContain('tailwindcss.com')
        expect(html).toContain('bg-gray-100')
        expect(html).toContain('min-h-screen')
      })
    })

    it('should have consistent meta tags across routes', async () => {
      const routes = ['/', '/profile']
      
      for (const route of routes) {
        const response = await app.handle(new Request(`http://localhost${route}`))
        const html = await response.text()
        
        expect(html).toContain('<meta charset="UTF-8"')
        expect(html).toContain('<meta name="viewport"')
        expect(html).toContain('<title>Login - Twitter Auth App</title>')
      }
    })
  })

  describe('Error Handling', () => {
    it('should handle 404 errors gracefully', async () => {
      const response = await app.handle(new Request('http://localhost/does-not-exist'))
      expectStatus(response, 404)
    })

    it('should handle invalid HTTP methods', async () => {
      const methods = ['POST', 'PUT', 'DELETE', 'PATCH']
      
      for (const method of methods) {
        const response = await app.handle(new Request('http://localhost/', { method }))
        // Should return either 404 or 405
        expect([404, 405]).toContain(response.status)
      }
    })

    it('should handle malformed requests', async () => {
      // Test with invalid URL
      try {
        const response = await app.handle(new Request('http://localhost/profile/../../../etc/passwd'))
        // Should either normalize the path or return an error
        expect([200, 404]).toContain(response.status)
      } catch (error) {
        // If it throws, that's also acceptable for security
        expect(error).toBeDefined()
      }
    })
  })

  describe('Performance Tests', () => {
    it('should handle concurrent requests', async () => {
      const concurrentRequests = 10
      const requests = Array.from({ length: concurrentRequests }, () =>
        app.handle(new Request('http://localhost/'))
      )
      
      const startTime = Date.now()
      const responses = await Promise.all(requests)
      const endTime = Date.now()
      
      // All requests should succeed
      responses.forEach(response => {
        expectSuccessfulResponse(response)
      })
      
      // Should handle concurrent requests reasonably fast
      expect(endTime - startTime).toBeLessThan(2000)
    })

    it('should maintain performance under load', async () => {
      const iterations = 50
      const times: number[] = []
      
      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now()
        const response = await app.handle(new Request('http://localhost/'))
        const endTime = Date.now()
        
        expectSuccessfulResponse(response)
        times.push(endTime - startTime)
      }
      
      // Calculate average response time
      const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length
      
      // Average response time should be reasonable
      expect(averageTime).toBeLessThan(100)
    })
  })

  describe('Memory and Resource Management', () => {
    it('should not leak memory with repeated requests', async () => {
      const initialMemory = process.memoryUsage().heapUsed
      
      // Make many requests
      for (let i = 0; i < 100; i++) {
        const response = await app.handle(new Request('http://localhost/'))
        expectSuccessfulResponse(response)
        await response.text() // Consume the response
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      const finalMemory = process.memoryUsage().heapUsed
      const memoryIncrease = finalMemory - initialMemory
      
      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
    })
  })
})
