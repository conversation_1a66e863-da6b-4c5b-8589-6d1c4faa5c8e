/**
 * Tests for authentication models and schemas
 */

import { describe, it, expect, beforeAll } from 'bun:test'
import { setupTestEnvironment } from '../config/test-env'

describe('Authentication Models', () => {
  beforeAll(() => {
    setupTestEnvironment()
  })

  describe('User Schema', () => {
    it('should import user schemas without errors', async () => {
      const schemas = await import('~/models/schema/auth.schema')

      expect(schemas.users).toBeDefined()
      expect(schemas.insertUserSchema).toBeDefined()
      expect(schemas.selectUserSchema).toBeDefined()
    })

    it('should validate correct user data', async () => {
      const { insertUserSchema } = await import('~/models/schema/auth.schema')

      const validUserData = {
        twitterId: 'twitter123',
        username: 'testuser',
        displayName: 'Test User',
        profileImageUrl: 'https://example.com/avatar.jpg',
        email: '<EMAIL>',
      }

      const result = insertUserSchema.safeParse(validUserData)
      expect(result.success).toBe(true)
    })

    it('should reject invalid user data', async () => {
      const { insertUserSchema } = await import('~/models/schema/auth.schema')

      const invalidUserData = {
        // Missing required fields
        username: 'testuser',
      }

      const result = insertUserSchema.safeParse(invalidUserData)
      expect(result.success).toBe(false)
    })

    it('should handle optional fields correctly', async () => {
      const { insertUserSchema } = await import('~/models/schema/auth.schema')

      const minimalUserData = {
        twitterId: 'twitter123',
        username: 'testuser',
        displayName: 'Test User',
        // profileImageUrl and email are optional
      }

      const result = insertUserSchema.safeParse(minimalUserData)
      expect(result.success).toBe(true)
    })
  })

  describe('Session Schema', () => {
    it('should import session schemas without errors', async () => {
      const schemas = await import('~/models/schema/auth.schema')

      expect(schemas.sessions).toBeDefined()
      expect(schemas.insertSessionSchema).toBeDefined()
      expect(schemas.selectSessionSchema).toBeDefined()
    })

    it('should validate correct session data', async () => {
      const { insertSessionSchema } = await import(
        '~/models/schema/auth.schema'
      )

      const validSessionData = {
        id: 'session123',
        userId: 1,
        expiresAt: new Date(Date.now() + 86400000), // 24 hours from now
      }

      const result = insertSessionSchema.safeParse(validSessionData)
      expect(result.success).toBe(true)
    })

    it('should reject invalid session data', async () => {
      const { insertSessionSchema } = await import(
        '~/models/schema/auth.schema'
      )

      const invalidSessionData = {
        // Missing required fields
        id: 'session123',
      }

      const result = insertSessionSchema.safeParse(invalidSessionData)
      expect(result.success).toBe(false)
    })

    it('should handle date fields correctly', async () => {
      const { insertSessionSchema } = await import(
        '~/models/schema/auth.schema'
      )

      const sessionData = {
        id: 'session123',
        userId: 1,
        expiresAt: new Date(),
      }

      const result = insertSessionSchema.safeParse(sessionData)
      expect(result.success).toBe(true)

      if (result.success) {
        expect(result.data.expiresAt).toBeInstanceOf(Date)
      }
    })
  })

  describe('Database Connection', () => {
    it('should create database instance without errors', async () => {
      const { db } = await import('~/models')
      expect(db).toBeDefined()
    })

    it('should have schema exported correctly', async () => {
      const schema = await import('~/models/schema')

      expect(schema.users).toBeDefined()
      expect(schema.sessions).toBeDefined()
      expect(schema.insertUserSchema).toBeDefined()
      expect(schema.selectUserSchema).toBeDefined()
      expect(schema.insertSessionSchema).toBeDefined()
      expect(schema.selectSessionSchema).toBeDefined()
    })
  })

  describe('Type Definitions', () => {
    it('should export correct TypeScript types', async () => {
      // This test ensures the types are properly exported
      // TypeScript will catch type errors at compile time
      const schemas = await import('~/models/schema/auth.schema')

      // These should be available as types
      expect(typeof schemas.insertUserSchema).toBe('object')
      expect(typeof schemas.selectUserSchema).toBe('object')
      expect(typeof schemas.insertSessionSchema).toBe('object')
      expect(typeof schemas.selectSessionSchema).toBe('object')
    })
  })

  describe('Schema Relationships', () => {
    it('should define proper foreign key relationships', async () => {
      const { sessions, users } = await import('~/models/schema/auth.schema')

      // Check that sessions table references users table
      expect(sessions).toBeDefined()
      expect(users).toBeDefined()

      // The actual foreign key constraint is defined in the schema
      // This test ensures the tables are properly defined
    })
  })
})
