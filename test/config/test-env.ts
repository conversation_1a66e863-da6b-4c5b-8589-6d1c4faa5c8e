/**
 * Test environment configuration
 * Sets up environment variables and configurations specific to testing
 */

/**
 * Test environment variables
 * These override the production environment variables during testing
 */
export const TEST_ENV = {
  NODE_ENV: 'test',
  DATABASE_URL: ':memory:', // Use in-memory SQLite for tests
  APP_URL: 'http://localhost:3000',
  APP_SECRET: 'test-secret-key-for-testing-only-not-secure',
  
  // Mock Twitter API credentials for testing
  TWITTER_CONSUMER_KEY: 'test-consumer-key',
  TWITTER_CONSUMER_SECRET: 'test-consumer-secret',
  TWITTER_ACCESS_TOKEN: 'test-access-token',
  TWITTER_ACCESS_SECRET: 'test-access-secret',
  TWITTER_CLIENT_ID: 'test-client-id',
  TWITTER_CLIENT_SECRET: 'test-client-secret',
  
  // Test-specific configurations
  HOST: '0.0.0.0',
  PORT: 3001, // Use different port for tests to avoid conflicts
}

/**
 * Apply test environment variables
 */
export function setupTestEnvironment() {
  Object.entries(TEST_ENV).forEach(([key, value]) => {
    process.env[key] = value.toString()
  })
}

/**
 * Clean up test environment
 */
export function cleanupTestEnvironment() {
  Object.keys(TEST_ENV).forEach(key => {
    delete process.env[key]
  })
}

/**
 * Test database configuration
 */
export const TEST_DB_CONFIG = {
  url: ':memory:',
  // Add any test-specific database configurations here
}

/**
 * Test server configuration
 */
export const TEST_SERVER_CONFIG = {
  port: 3001,
  host: '0.0.0.0',
  // Add any test-specific server configurations here
}
