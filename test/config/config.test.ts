/**
 * Tests for application configuration
 */

import { describe, it, expect, beforeAll, afterAll } from 'bun:test'
import { setupTestEnvironment, cleanupTestEnvironment, TEST_ENV } from './test-env'

describe('Configuration Tests', () => {
  beforeAll(() => {
    setupTestEnvironment()
  })

  afterAll(() => {
    cleanupTestEnvironment()
  })

  describe('Environment Variables', () => {
    it('should have all required environment variables set', () => {
      expect(process.env.NODE_ENV).toBe('test')
      expect(process.env.DATABASE_URL).toBe(':memory:')
      expect(process.env.APP_URL).toBe('http://localhost:3000')
      expect(process.env.APP_SECRET).toBeDefined()
    })

    it('should have Twitter API credentials configured', () => {
      expect(process.env.TWITTER_CONSUMER_KEY).toBeDefined()
      expect(process.env.TWITTER_CONSUMER_SECRET).toBeDefined()
      expect(process.env.TWITTER_ACCESS_TOKEN).toBeDefined()
      expect(process.env.TWITTER_ACCESS_SECRET).toBeDefined()
      expect(process.env.TWITTER_CLIENT_ID).toBeDefined()
      expect(process.env.TWITTER_CLIENT_SECRET).toBeDefined()
    })

    it('should use test-specific values', () => {
      expect(process.env.NODE_ENV).toBe('test')
      expect(process.env.PORT).toBe('3001')
      expect(process.env.DATABASE_URL).toBe(':memory:')
    })
  })

  describe('Configuration Loading', () => {
    it('should load auth configuration without errors', async () => {
      try {
        const { authSchema } = await import('~/config/auth.config')
        const result = authSchema.safeParse(process.env)
        expect(result.success).toBe(true)
      } catch (error) {
        throw new Error(`Failed to load auth config: ${error}`)
      }
    })

    it('should load database configuration without errors', async () => {
      try {
        const { dbSchema } = await import('~/config/db.config')
        const result = dbSchema.safeParse(process.env)
        expect(result.success).toBe(true)
      } catch (error) {
        throw new Error(`Failed to load db config: ${error}`)
      }
    })

    it('should load main configuration without errors', async () => {
      try {
        // Ensure environment variables are set before importing config
        expect(process.env.NODE_ENV).toBe('test')
        expect(process.env.DATABASE_URL).toBe(':memory:')

        // Import and validate the config schemas directly
        const { authSchema } = await import('~/config/auth.config')
        const { dbSchema } = await import('~/config/db.config')

        // Test that schemas can parse the environment
        const authResult = authSchema.safeParse(process.env)
        const dbResult = dbSchema.safeParse(process.env)

        expect(authResult.success).toBe(true)
        expect(dbResult.success).toBe(true)
      } catch (error) {
        throw new Error(`Failed to load main config: ${error}`)
      }
    })
  })

  describe('Database Configuration', () => {
    it('should use in-memory database for tests', () => {
      expect(process.env.DATABASE_URL).toBe(':memory:')
    })

    it('should be able to create database connection', async () => {
      try {
        const { db } = await import('~/models')
        expect(db).toBeDefined()
      } catch (error) {
        throw new Error(`Failed to create database connection: ${error}`)
      }
    })
  })

  describe('Schema Validation', () => {
    it('should validate auth schema with test environment', async () => {
      const { authSchema } = await import('~/config/auth.config')

      const testAuthData = {
        TWITTER_CONSUMER_KEY: process.env.TWITTER_CONSUMER_KEY,
        TWITTER_CONSUMER_SECRET: process.env.TWITTER_CONSUMER_SECRET,
        TWITTER_ACCESS_TOKEN: process.env.TWITTER_ACCESS_TOKEN,
        TWITTER_ACCESS_SECRET: process.env.TWITTER_ACCESS_SECRET,
        TWITTER_CLIENT_ID: process.env.TWITTER_CLIENT_ID,
        TWITTER_CLIENT_SECRET: process.env.TWITTER_CLIENT_SECRET,
      }

      const result = authSchema.safeParse(testAuthData)
      expect(result.success).toBe(true)
    })

    it('should validate database schema with test environment', async () => {
      const { dbSchema } = await import('~/config/db.config')

      const testDbData = {
        DATABASE_URL: process.env.DATABASE_URL,
      }

      const result = dbSchema.safeParse(testDbData)
      expect(result.success).toBe(true)
    })

    it('should handle missing environment variables gracefully', async () => {
      const { authSchema } = await import('~/config/auth.config')

      const incompleteData = {
        TWITTER_CONSUMER_KEY: 'test',
        // Missing other required fields
      }

      const result = authSchema.safeParse(incompleteData)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues.length).toBeGreaterThan(0)
      }
    })
  })
})
