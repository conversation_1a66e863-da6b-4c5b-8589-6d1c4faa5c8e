/**
 * Test utilities and helpers for Elysia application testing
 */

import { Elysia } from 'elysia'
import { html } from '@elysiajs/html'

/**
 * Creates a test instance of the Elysia app with necessary plugins
 */
export function createTestApp() {
  return new Elysia().use(html())
}

/**
 * Helper to extract text content from HTML response
 */
export function extractTextFromHTML(html: string): string {
  // Simple regex to remove HTML tags for testing purposes
  return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
}

/**
 * Helper to check if HTML contains specific elements
 */
export function containsElement(html: string, selector: string): boolean {
  // Simple check for common HTML elements
  const patterns = {
    'h1': /<h1[^>]*>.*?<\/h1>/i,
    'h2': /<h2[^>]*>.*?<\/h2>/i,
    'h3': /<h3[^>]*>.*?<\/h3>/i,
    'div': /<div[^>]*>.*?<\/div>/i,
    'a': /<a[^>]*>.*?<\/a>/i,
    'button': /<button[^>]*>.*?<\/button>/i,
    'form': /<form[^>]*>.*?<\/form>/i,
  }
  
  const pattern = patterns[selector as keyof typeof patterns]
  return pattern ? pattern.test(html) : html.includes(selector)
}

/**
 * Helper to check if HTML contains specific CSS classes
 */
export function containsClass(html: string, className: string): boolean {
  const classPattern = new RegExp(`class="[^"]*\\b${className}\\b[^"]*"`, 'i')
  return classPattern.test(html)
}

/**
 * Helper to validate HTML structure
 */
export function isValidHTML(html: string): boolean {
  // Basic HTML validation - check for proper html, head, body structure
  return (
    html.includes('<html') &&
    html.includes('<head>') &&
    html.includes('<body') &&
    html.includes('</html>')
  )
}

/**
 * Helper to check if response contains meta tags
 */
export function containsMetaTags(html: string): boolean {
  return (
    html.includes('<meta charset="UTF-8"') &&
    html.includes('<meta name="viewport"')
  )
}

/**
 * Helper to extract title from HTML
 */
export function extractTitle(html: string): string | null {
  const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i)
  return titleMatch ? titleMatch[1].trim() : null
}

/**
 * Helper to check if HTML includes Tailwind CSS
 */
export function includesTailwindCSS(html: string): boolean {
  return html.includes('tailwindcss.com')
}

/**
 * Mock request helper for testing
 */
export function createMockRequest(path: string, method: string = 'GET', headers: Record<string, string> = {}) {
  return new Request(`http://localhost:3000${path}`, {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
    },
  })
}
