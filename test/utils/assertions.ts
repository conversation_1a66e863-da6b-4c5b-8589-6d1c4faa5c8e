/**
 * Custom assertion helpers for testing Elysia routes
 */

import { expect } from 'bun:test'

/**
 * Assert that response is successful (2xx status code)
 */
export function expectSuccessfulResponse(response: Response) {
  expect(response.status).toBeGreaterThanOrEqual(200)
  expect(response.status).toBeLessThan(300)
}

/**
 * Assert that response has specific status code
 */
export function expectStatus(response: Response, expectedStatus: number) {
  expect(response.status).toBe(expectedStatus)
}

/**
 * Assert that response has HTML content type
 */
export function expectHTMLContentType(response: Response) {
  const contentType = response.headers.get('content-type')
  expect(contentType).toContain('text/html')
}

/**
 * Assert that HTML contains specific text
 */
export function expectHTMLToContain(html: string, text: string) {
  expect(html).toContain(text)
}

/**
 * Assert that HTML contains specific element
 */
export function expectHTMLToContainElement(html: string, element: string) {
  const elementPattern = new RegExp(`<${element}[^>]*>`, 'i')
  expect(elementPattern.test(html)).toBe(true)
}

/**
 * Assert that HTML has proper structure
 */
export function expectValidHTMLStructure(html: string) {
  // Check for DOCTYPE (case insensitive)
  expect(html.toLowerCase()).toContain('<!doctype html>')
  expect(html).toContain('<html')
  expect(html).toContain('<head>')
  expect(html).toContain('<body')
  expect(html).toContain('</html>')
}

/**
 * Assert that HTML contains required meta tags
 */
export function expectRequiredMetaTags(html: string) {
  expect(html).toContain('<meta charset="UTF-8"')
  expect(html).toContain('<meta name="viewport"')
}

/**
 * Assert that HTML contains specific CSS classes
 */
export function expectHTMLToContainClass(html: string, className: string) {
  const classPattern = new RegExp(`class="[^"]*\\b${className}\\b[^"]*"`, 'i')
  expect(classPattern.test(html)).toBe(true)
}

/**
 * Assert that HTML contains Tailwind CSS
 */
export function expectTailwindCSS(html: string) {
  expect(html).toContain('tailwindcss.com')
}

/**
 * Assert that HTML has specific title
 */
export function expectTitle(html: string, expectedTitle: string) {
  const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i)
  expect(titleMatch).toBeTruthy()
  if (titleMatch && titleMatch[1]) {
    expect(titleMatch[1].trim()).toBe(expectedTitle)
  }
}

/**
 * Assert that response time is within acceptable range
 */
export function expectReasonableResponseTime(startTime: number, maxMs: number = 1000) {
  const responseTime = Date.now() - startTime
  expect(responseTime).toBeLessThan(maxMs)
}
